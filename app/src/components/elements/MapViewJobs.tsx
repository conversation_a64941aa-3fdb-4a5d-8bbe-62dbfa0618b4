import React, { useEffect, useMemo, useRef, useState } from 'react';
import { View, Text, useWindowDimensions, TouchableOpacity, StyleSheet, Platform, PermissionsAndroid, Alert } from 'react-native';
import MapView, { Marker, Callout } from 'react-native-maps';
import moment from 'moment';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Geolocation from 'react-native-geolocation-service';
import JobsCard from '../JobsCard';
import { useGetFavoriteJobList } from '../../services/jobsService';

const MapViewJobs = ({ list, onJobPress, isFullAddress }: any) => {
  const { width } = useWindowDimensions();
  const mapRef = useRef(null);
  const markersRef = useRef([]);
  const [initialRegion, setInitialRegion] = useState({
    latitude: 51.5285257,
    longitude: -0.2667455,
    latitudeDelta: 5,
    longitudeDelta: 5,
  });

  const { list: favList, refetch: refetchFavList } = useGetFavoriteJobList();

  useEffect(() => {
    console.log({ list });
  }, [list]);

  const [location, setLocation] = useState({
    latitude: 51.5285257,
    longitude: -0.2667455,
  });

  const getMyLocation = async () => {
    //ask for permission denied
    if (Platform.OS === 'ios') {
      const permissionInUse = await Geolocation.requestAuthorization('whenInUse');
      const permissionAlways = await Geolocation.requestAuthorization('always');
      if (permissionInUse === 'denied' && permissionAlways === 'denied') {
        Alert.alert('Location permission denied');
        return {
          latitude: 51.5285257,
          longitude: -0.2667455,
        };
      } else if (permissionInUse === 'granted' || permissionAlways === 'granted') {
        Geolocation.getCurrentPosition(
          (position) => {
            setLocation({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            });
          },
          (error) => {
            console.error('Error getting location', error);
          },
          { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
        );
      }
    }
  };

  const listWithLocation = useMemo(() => {
    return list.filter((item: any) => item.location_lat && item.location_long && item.location_lat.length > 0 && item.location_long.length > 0);
  }, [list]);

  useEffect(() => {
    if (listWithLocation.length === 0) {
      return;
    }
    setInitialRegion({
      latitude: parseFloat(listWithLocation[0].location_lat),
      longitude: parseFloat(listWithLocation[0].location_long),
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    });
    markersRef.current.forEach((marker: any) => {
      marker.showCallout();
    });
  }, [listWithLocation]);

  useEffect(() => {
    getMyLocation();
  }, []);

  useEffect(() => {
    mapRef.current?.animateToRegion({
      latitude: location.latitude,
      longitude: location.longitude,
      latitudeDelta: 0.0922,
      longitudeDelta: 0.0421,
    });
  }, [location]);

  return (
    <View style={{ flex: 1 }}>
      <MapView
        showsUserLocation
        showsMyLocationButton
        zoomEnabled
        initialRegion={initialRegion}
        style={{
          flex: 1,
          width: '100%',
          height: '100%',
        }}
        // provider='google'
        ref={mapRef}
      >
        {listWithLocation.map((item: any, index: number) => {
          // const jobStatus = item.job_status === 'Closed' ? 'Closed' : 'Open';
          const companyName = item.user_post?.[0]?.user_name;
          return (
            <Marker
              key={index}
              coordinate={{
                latitude: parseFloat(isFullAddress ? item.location_lat : item.location_lat) + Math.random() * 0.0001 - 0.00005,
                longitude: parseFloat(isFullAddress ? item.location_long : item.location_long) + Math.random() * 0.0001 - 0.00005,
              }}
              ref={(ref) => {
                markersRef.current[index] = ref;
              }}
            >
              <Callout
                onPress={() => {
                  onJobPress(item);
                }}
                style={{
                  width: width * 0.8,
                }}
              >
                <View style={{ padding: 3 }}>
                  <JobsCard
                    showAvatar={false}
                    key={'job-card-' + index}
                    company={item.user_post?.[0] || undefined}
                    name={item.job_status === 'Closed' ? 'Closed' : 'Open'}
                    title={item.task}
                    date={`${moment(item.start_date_required).format('DD/MM/YYYY')}`}
                    isExpired={item.start_date_required && moment(item.start_date_required).isBefore(moment().startOf('day'))}
                    location={item.location}
                    onPress={() => {
                      // navigation.navigate('ApplyJobDetail', {
                      //   activeJob: item,
                      // });
                    }}
                    reviewedNumber={item.reviews?.length || 0}
                    reviewedRating={
                      item.reviews && item.reviews?.length > 0
                        ? item.reviews?.reduce((accumulator, currentValue) => accumulator + parseFloat(currentValue.rate_number), 0) /
                          item.reviews?.length
                        : 0
                    }
                    isFavorite={favList.includes(String(item.id))}
                    style={{
                      marginBottom: 10,
                    }}
                  />

                  {/* <JobsCard
                    company={item.user_post?.[0] || undefined}
                    name={jobStatus}
                    title={item.task}
                    status={`£${item.price}/h - ${moment(item.date_required).format('DD/MM/YYYY')} - ${item.location}`}
                    onPress={undefined}
                  /> */}
                  {/* <Text style={{ fontSize: 15, marginBottom: 10 }}>{item.task}</Text>
                  <Text style={{ fontSize: 12 }}>{companyName}</Text>
                  <Text style={{ fontSize: 12, width: '100%', flexWrap: 'wrap' }}>{`${moment(item.date_required).format('DD/MM/YYYY')} - ${
                    isFullAddress ? item.location : item.short_location
                  }`}</Text> */}
                </View>
              </Callout>
            </Marker>
          );
        })}
      </MapView>
      <TouchableOpacity style={styles.myLocationButtonStyle} onPress={async () => await getMyLocation()}>
        <View>
          <Icon name="my-location" size={20} color="black" />
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default MapViewJobs;

const styles = StyleSheet.create({
  myLocationButtonStyle: {
    position: 'absolute',
    bottom: 15,
    right: 15,
    backgroundColor: 'white',
    borderRadius: 40,
    width: 40,
    height: 40,
    padding: 10,
  },
});
