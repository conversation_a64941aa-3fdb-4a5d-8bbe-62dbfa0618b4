import React, { useCallback, useEffect, useState } from 'react';

import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import Header from './Header';
import { Colors } from 'react-native/Libraries/NewAppScreen';
import {
  View,
  Text,
  Dimensions,
  Pressable,
  ActivityIndicator,
  TouchableOpacity,
  ImageBackground,
  Platform,
  PermissionsAndroid,
  StyleSheet,
  Switch,
  TextInput,
} from 'react-native';
import moment from 'moment';
import CustomButton from './CustomButton';
import JobsCard from './JobsCard';
import CustomText from './CustomText';
import { useAuth } from '../../context/AuthContext';
import MapViewJobs from './elements/MapViewJobs';
import { hideJob, useListTradespersonJobs } from '../services/jobsService';
import DateRangePicker from 'rn-select-date-range';
import SelectDropdown from 'react-native-select-dropdown';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Modal from 'react-native-modal';
import { BackgroundGradient } from '../assets/image';
import Popover from 'react-native-popover-view';
import Geolocation from 'react-native-geolocation-service';
import { getTradeOptions } from '../constants/tradeOptions';

const locationRadius = [
  { title: 'All', value: '0' },
  { title: '5 Miles', value: '5' },
  { title: '10 Miles', value: '10' },
  { title: '20 Miles', value: '20' },
  { title: '50 Miles', value: '50' },
  { title: '50+ Miles', value: '50+' },
];

const FilterComponent = ({
  date,
  setDate,
  setSortTimeBy,
  setTrade,
  onSearch,
  searchRadius,
  setSearchRadius,
  sortTimeBy,
  isMapView,
  setIsFavoriteFilter,
  isFavoriteFilter,
  selectedTrade,
  customTrade,
  setCustomTrade,
}) => {
  const [selectRange, setSelectRange] = useState<{ firstDate?: string; secondDate?: string }>({});
  const [modalApplyVisible, setModalVisible] = useState(false);

  useEffect(() => {
    setSelectRange(date);
  }, [date]);

  const tradeOptions = getTradeOptions();

  return (
    <View style={{ flexDirection: 'column', justifyContent: 'space-between', alignItems: 'center', backgroundColor: Colors.white }}>
      <View style={{ flexDirection: 'column', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 0 }}>
        {!isMapView && (
          <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 10 }}>
            <CustomText style={{ width: 120 }} text="Sort by: " color="black" />
            <SelectDropdown
              data={[
                { title: 'Oldest', value: 'oldest' },
                { title: 'Newest', value: 'newest' },
              ]}
              defaultValue={sortTimeBy === 'oldest' ? { title: 'Oldest', value: 'oldest' } : { title: 'Newest', value: 'newest' }}
              onSelect={(selectedItem) => {
                setSortTimeBy(selectedItem.value);
              }}
              renderButton={(selectedItem, isOpened) => {
                return (
                  <View style={styles.dropdownButton}>
                    <Text style={styles.dropdownButtonText}>{selectedItem && selectedItem.title}</Text>
                    <Icon name={isOpened ? 'chevron-up' : 'chevron-down'} style={styles.dropdownButtonArrow} />
                  </View>
                );
              }}
              renderItem={(item, index, isSelected) => {
                return (
                  <View style={{ ...styles.dropdownItem, ...(isSelected && { backgroundColor: '#D2D9DF' }) }}>
                    <Text style={styles.dropdownItemText}>{item.title}</Text>
                  </View>
                );
              }}
              showsVerticalScrollIndicator={false}
              dropdownStyle={styles.dropdownMenu}
            />
          </View>
        )}

        <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 10, marginTop: 15 }}>
          <CustomText style={{ width: 120 }} text="Trade: " color="black" />
          <SelectDropdown
            data={tradeOptions}
            defaultValue={{ title: 'All', label: 'All', value: 'All' }}
            onSelect={(selectedItem) => {
              setTrade(selectedItem.value);
              if (selectedItem.value !== 'Other') {
                setCustomTrade('');
              }
            }}
            renderButton={(selectedItem, isOpened) => {
              return (
                <View style={[styles.dropdownButton, { width: 210 }]}>
                  <Text style={styles.dropdownButtonText}>{selectedItem && selectedItem.title}</Text>
                  <Icon name={isOpened ? 'chevron-up' : 'chevron-down'} style={styles.dropdownButtonArrow} />
                </View>
              );
            }}
            renderItem={(item, index, isSelected) => {
              return (
                <View style={{ ...styles.dropdownItem, ...(isSelected && { backgroundColor: '#D2D9DF' }) }}>
                  <Text style={styles.dropdownItemText}>{item.title}</Text>
                </View>
              );
            }}
            showsVerticalScrollIndicator={false}
            dropdownStyle={styles.dropdownMenu}
          />
        </View>

        {selectedTrade === 'Other' && (
          <View style={{ flexDirection: 'row', alignItems: 'center', marginRight: 10, marginTop: 15 }}>
            <CustomText style={{ width: 120 }} text="Custom Trade: " color="black" />
            <TextInput
              placeholder="Enter trade"
              value={customTrade}
              onChangeText={setCustomTrade}
              style={[styles.dropdownButton, { width: 210, paddingHorizontal: 12, fontSize: 16 }]}
            />
          </View>
        )}

        {/* <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: !isMapView ? 15 : 0 }}> */}
        {/* <CustomText style={{ width: 120 }} text="Trade: " color="black" /> */}
        {/* <TextInput placeholder="Trade" value={trade} style={{ ...filterInput, flex: 1 }} onChangeText={setTrade} /> */}
        {/* </View> */}

        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 15 }}>
          <CustomText style={{ width: 120 }} text="Date: " color="black" />

          <View style={[styles.dropdownButton, { width: 210 }]}>
            <TouchableOpacity
              onPress={() => {
                setModalVisible(!modalApplyVisible);
              }}
            >
              <Text style={{ fontWeight: '600', color: '#000', fontSize: 15 }}>
                {selectRange?.firstDate && selectRange?.secondDate ? (
                  <>
                    {selectRange.firstDate === selectRange.secondDate
                      ? `${selectRange.firstDate}`
                      : `${selectRange.firstDate} - ${selectRange.secondDate}`}
                  </>
                ) : (
                  <>Select date</>
                )}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 15 }}>
          <CustomText style={{ width: 120 }} text="Location radius: " color="black" />
          <SelectDropdown
            data={locationRadius}
            defaultValue={locationRadius.find((item) => item.value === searchRadius)}
            onSelect={(selectedItem) => {
              setSearchRadius(selectedItem.value);
            }}
            renderButton={(selectedItem, isOpened) => {
              return (
                <View style={styles.dropdownButton}>
                  <Text style={styles.dropdownButtonText}>{selectedItem && selectedItem.title}</Text>
                  <Icon name={isOpened ? 'chevron-up' : 'chevron-down'} style={styles.dropdownButtonArrow} />
                </View>
              );
            }}
            renderItem={(item, index, isSelected) => {
              return (
                <View style={{ ...styles.dropdownItem, ...(isSelected && { backgroundColor: '#D2D9DF' }) }}>
                  <Text style={styles.dropdownItemText}>{item.title}</Text>
                </View>
              );
            }}
            showsVerticalScrollIndicator={false}
            dropdownStyle={styles.dropdownMenu}
          />
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginTop: 15 }}>
          <CustomText style={{ width: 120 }} text="Favourite: " color="black" />
          <Switch
            trackColor={{ false: '#767577', true: '#81b0ff' }}
            thumbColor={isFavoriteFilter ? '#f5dd4b' : '#f4f3f4'}
            onValueChange={() => setIsFavoriteFilter(!isFavoriteFilter)}
            value={isFavoriteFilter}
          />
        </View>
      </View>
      <View style={{ flexDirection: 'row', gap: 10, justifyContent: 'center', alignItems: 'center', alignSelf: 'center', marginTop: 15 }}>
        <CustomButton
          color="white"
          style={{ flex: 2, marginTop: 5, borderRadius: 40, height: 40, borderWidth: 1 }}
          title="Reset"
          onPress={() => {
            setTrade('All');
            setCustomTrade('');
            setDate({});
            setSelectRange({});
            setSortTimeBy('newest');
          }}
        />
        <CustomButton
          color="black"
          style={{ borderRadius: 40, height: 40, textAlign: 'center', flex: 4, borderWidth: 1 }}
          title="Search"
          onPress={() => {
            onSearch();
          }}
        />
      </View>
      <Modal
        style={{ justifyContent: 'center', alignItems: 'center' }}
        isVisible={modalApplyVisible}
        onBackdropPress={() => {
          setModalVisible(!modalApplyVisible);
        }}
      >
        <View style={styles.modalApply}>
          <DateRangePicker
            onSelectDateRange={(range) => {
              setSelectRange(range as unknown as { firstDate: string; secondDate: string });
            }}
            onConfirm={() => {
              setDate(selectRange);
              setModalVisible(!modalApplyVisible);
            }}
            blockSingleDateSelection={false}
            responseFormat="YYYY-MM-DD"
            selectedDateContainerStyle={{
              height: 35,
              width: '100%',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'blue',
            }}
            selectedDateStyle={{
              fontWeight: 'bold',
              color: 'white',
            }}
          />
        </View>
      </Modal>
    </View>
  );
};

export default function FindJob({ route, navigation }) {
  const { authState } = useAuth();
  const [isMapView, setIsMapView] = React.useState(false);
  const [selectDate, setDate] = React.useState<{ firstDate?: string; secondDate?: string }>({});
  const [tradeFilter, setTradeFilter] = React.useState('All');
  const [customTrade, setCustomTrade] = React.useState('');
  const [sortTimeBy, setSortTimeBy] = React.useState('newest');
  const [searchRadius, setSearchRadius] = React.useState('0');
  const [location, setLocation] = React.useState({
    latitude: 51.5285257,
    longitude: -0.2667455,
  });

  const [isFavoriteFilter, setIsFavoriteFilter] = React.useState(false);

  const [isLoading, setIsLoading] = React.useState(false);

  const [listJobs, setListJobs] = React.useState([]);

  // Remove old favorite list logic - now using isFavorite from API
  // const { list: favList } = useGetFavoriteJobList();

  const { isFetching, list, reFetch } = useListTradespersonJobs({
    owner_id: authState.user?.id,
    ...(tradeFilter && tradeFilter !== 'All' && { trade: tradeFilter === 'Other' ? customTrade : tradeFilter }),
    ...(searchRadius && { searchRadius: searchRadius, location: location }),
    ...(selectDate.firstDate && selectDate.secondDate && { firstDate: selectDate.firstDate, secondDate: selectDate.secondDate }),
    job_status: 'Open',
  });
  const [isFilterVisible, setFilterVisible] = useState(false);

  const handleHideExpiredJobs = () => {
    // get expired jobs
    const expiredJobs = list.filter((item) => item.start_date_required && moment(item.start_date_required).isBefore(moment()));
    const expiredJobsId = expiredJobs.map((item) => item.id);
    setIsLoading(true);
    hideJob(expiredJobsId)
      .then(() => {
        reFetch();
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  const onHandleSearch = useCallback(async () => {
    await reFetch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectDate, tradeFilter, searchRadius]);

  const sortJobs = (list, sortTimeBy) => {
    const compareFn = sortTimeBy === 'newest' ? (a, b) => moment(b.date).diff(moment(a.date)) : (a, b) => moment(a.date).diff(moment(b.date));

    return list.sort(compareFn);
  };

  useEffect(() => {
    if (list) {
      const sortedJobs = sortJobs(list, sortTimeBy);
      if (isFavoriteFilter) {
        // Use isFavorite from API data instead of separate favList
        setListJobs(sortedJobs.filter((item) => item.isFavorite === true));
      } else {
        setListJobs(sortedJobs);
      }
    }
  }, [sortTimeBy, list, isFavoriteFilter]);
  useEffect(() => {
    // navigation focus event
    const getCurrentPosition = () => {
      if (Platform.OS === 'android') {
        PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION).then((granted) => {
          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            Geolocation.getCurrentPosition(
              (position) => {
                setLocation({
                  latitude: position.coords.latitude,
                  longitude: position.coords.longitude,
                });
              },
              (error) => {
                console.log('error', error);
              },
              { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
            );
          } else {
            console.log('Location permission denied');
          }
        });
      } else {
        Geolocation.getCurrentPosition(
          (position) => {
            setLocation({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            });
          },
          (error) => {
            console.log('error', error);
          },
          { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
        );
      }
    };

    getCurrentPosition();

    const unsubscribe = navigation.addListener('focus', () => {
      reFetch();
    });
    return () => {
      unsubscribe();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <SafeAreaView style={styles.background}>
      <ImageBackground source={BackgroundGradient} style={{ height: Dimensions.get('window').height }}>
        <Header title="Current jobs posted" navigation={navigation} hasBack={route.params?.isNoti ? false : true} />
        <View style={{ flex: 1, paddingBottom: 40, marginTop: 20 }}>
          <View style={{ flexDirection: 'row', marginHorizontal: 15, marginBottom: 15, justifyContent: 'space-evenly' }}>
            <View>
              <CustomButton
                color="black"
                style={{
                  width: Dimensions.get('window').width / 2 - 30,
                  backgroundColor: '#1b53ac',
                  borderRadius: 25,
                  padding: 15,
                  height: 45,
                }}
                isLoading={isLoading}
                onPress={() => {
                  if (list.filter((item) => item.start_date_required && moment(item.start_date_required).isBefore(moment())).length === 0) {
                    return;
                  }
                  handleHideExpiredJobs();
                }}
                title="Clear expired jobs"
              />
            </View>

            <View style={{ backgroundColor: Colors.white, borderRadius: 25 }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                <Popover
                  isVisible={isFilterVisible}
                  arrowSize={{ width: 0, height: 0 }}
                  onRequestClose={() => setFilterVisible(false)}
                  popoverStyle={{ backgroundColor: Colors.white, borderRadius: 25, padding: 15, width: Dimensions.get('window').width - 30 }}
                  from={
                    <TouchableOpacity
                      onPress={() => setFilterVisible(true)}
                      style={{
                        height: 45,
                        flexDirection: 'row',
                        width: Dimensions.get('window').width / 2 - 30,
                        alignSelf: 'center',
                        justifyContent: 'center',
                        alignItems: 'center',
                        padding: 0,
                      }}
                    >
                      <Text style={{ fontSize: 16 }}>Filter</Text>
                    </TouchableOpacity>
                  }
                >
                  <FilterComponent
                    isMapView={isMapView}
                    setSortTimeBy={setSortTimeBy}
                    sortTimeBy={sortTimeBy}
                    setTrade={setTradeFilter}
                    onSearch={onHandleSearch}
                    setDate={setDate}
                    date={selectDate}
                    searchRadius={searchRadius}
                    setSearchRadius={setSearchRadius}
                    setIsFavoriteFilter={setIsFavoriteFilter}
                    isFavoriteFilter={isFavoriteFilter}
                    selectedTrade={tradeFilter}
                    customTrade={customTrade}
                    setCustomTrade={setCustomTrade}
                  />
                </Popover>
              </View>
            </View>
          </View>

          <View style={styles.jobContainer}>
            {isFetching ? (
              <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                <ActivityIndicator size="large" color={Colors.white} />
              </View>
            ) : (
              <>
                {listJobs.length === 0 ? (
                  <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
                    <Text style={{ color: Colors.white, fontSize: 23 }}>No jobs to display here!</Text>
                  </View>
                ) : (
                  <>
                    {isMapView ? (
                      <View style={{ flex: 1 }}>
                        <View
                          style={{
                            backgroundColor: Colors.white,
                            flex: 1,
                          }}
                        >
                          <MapViewJobs
                            list={listJobs}
                            isFullAddress
                            onJobPress={(job) => {
                              navigation.navigate('ApplyJobDetail', {
                                activeJob: job,
                              });
                            }}
                          />
                        </View>
                      </View>
                    ) : (
                      <View style={styles.cardContainer}>
                        <ScrollView>
                          {listJobs.map((item, index) => {
                            const jobStatus = item.job_status === 'Closed' ? 'Closed' : 'Open';
                            const application =
                              item.application &&
                              item.application?.length > 0 &&
                              item.application?.filter((app) => {
                                // console.log('app', app?.tradesperson, authState.user?.id);
                                return app?.tradesperson === authState.user?.id;
                              });
                            // console.log('application', application);
                            // console.log('=============================================');
                            if (application.length === 0 || !application) {
                              return (
                                <JobsCard
                                  key={'job-card-' + index}
                                  company={item.user_post?.[0] || undefined}
                                  name={jobStatus}
                                  title={item.task}
                                  date={`${moment(item.start_date_required).format('DD/MM/YYYY')}`}
                                  isExpired={item.start_date_required && moment(item.start_date_required).isBefore(moment().startOf('day'))}
                                  location={item.short_location}
                                  onPress={() => {
                                    navigation.navigate('ApplyJobDetail', {
                                      activeJob: item,
                                      onJobUpdate: () => {
                                        // Refetch jobs when favorite status changes
                                        reFetch();
                                      }
                                    });
                                  }}
                                  reviewedNumber={item.reviews?.length || 0}
                                  reviewedRating={
                                    item.reviews && item.reviews?.length > 0
                                      ? item.reviews?.reduce((accumulator, currentValue) => accumulator + parseFloat(currentValue.rate_number), 0) /
                                        item.reviews?.length
                                      : 0
                                  }
                                  isFavorite={item.isFavorite || false}
                                  style={{
                                    marginBottom: 10,
                                  }}
                                />
                              );
                            }
                          })}
                        </ScrollView>
                      </View>
                    )}
                  </>
                )}
              </>
            )}

            <View style={styles.twoColumn}>
              <Pressable style={[styles.button, styles.buttonWidth, !isMapView && styles.buttonActive]} onPress={() => setIsMapView(false)}>
                <Text style={[styles.buttonText, !isMapView && { color: Colors.white }]}>List View</Text>
              </Pressable>
              <Pressable style={[styles.button, styles.buttonWidth, isMapView && styles.buttonActive]} onPress={() => setIsMapView(true)}>
                <Text style={[styles.buttonText, isMapView && { color: Colors.white }]}>Map View</Text>
              </Pressable>
            </View>
          </View>
        </View>
      </ImageBackground>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  background: {
    backgroundColor: Colors.black,
    width: '100%',
    height: '100%',
  },
  jobContainer: {
    flexDirection: 'column',
    gap: 10,
    flex: 1,
    marginBottom: 60,
  },
  twoColumn: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
  },
  button: {
    backgroundColor: Colors.white,
    paddingVertical: 10,
  },
  buttonText: {
    color: Colors.black,
    textAlign: 'center',
    fontFamily: 'Montserrat-Regular',
  },
  buttonWidth: {
    width: (Dimensions.get('window').width - 32 - 10) / 2,
    borderWidth: 1,
    borderColor: Colors.black,
  },
  buttonActive: {
    backgroundColor: Colors.black,
    borderColor: Colors.white,
  },
  cardContainer: {
    flexDirection: 'column',
    flex: 1,
    paddingHorizontal: 15,
  },
  dropdownButton: {
    width: 150,
    height: 30,
    backgroundColor: '#E9ECEF',
    borderRadius: 40,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 12,
  },
  dropdownButtonText: {
    flex: 1,
    fontSize: 18,
    fontWeight: '500',
    color: '#151E26',
  },
  dropdownButtonArrow: {
    fontSize: 28,
  },
  dropdownItem: {
    width: '100%',
    flexDirection: 'row',
    paddingHorizontal: 12,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 8,
  },
  dropdownItemText: {
    flex: 1,
    fontSize: 18,
    fontWeight: '500',
    color: '#151E26',
  },
  dropdownMenu: {
    backgroundColor: '#E9ECEF',
    borderRadius: 8,
  },
  modalApply: {
    width: Dimensions.get('window').width - 50,
    backgroundColor: Colors.white,
    borderRadius: 25,
    padding: 20,
    height: undefined,
  },
});
