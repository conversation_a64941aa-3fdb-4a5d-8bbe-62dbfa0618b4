import { createContext, useContext, useEffect, useState } from 'react';
import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import auth from '@react-native-firebase/auth';
import { OneSignal } from 'react-native-onesignal';
import { useAtom } from 'jotai';
import { buttonNumbersAtom } from '../src/atoms/buttonNumbersAtom';
import KeychainService from '../src/services/KeychainService';

interface AuthProps {
  authState?: {
    token: string | null;
    authenticated: boolean | null;
    user: any;
    isTrade: boolean | undefined;
    didInitialFetch: boolean;
  };
  onRegister?: (data: any) => Promise<any>;
  onLogin?: (email: string, password: string, token?: string) => Promise<any>;
  onLogout?: () => Promise<any>;
  onForgotPassword?: (email: string) => Promise<any>;
  loadUser?: () => Promise<any>;
  updateUser?: (body: any) => Promise<any>;
}

interface registerProps {
  email: string;
  password: string;
  name: string;
  trade?: string;
  postcode?: string;
  qualifications?: string;
  documentation?: string;
  city?: string;
  county?: string;
  travel_distance?: string;
  type: string;
  documents?: string;
  companyAddress1?: string;
  companyContact?: string;
  orderNumber?: string;
  orderContactName?: string;
  orderContactEmail?: string;
  orderDocuments?: string;
  bio?: string;
}

export const TOKEN_KEY = 'my-jwt';
// export const API_URL = 'https://trademotionbackend.vercel.app';
export const API_URL = 'http://localhost:5351';
const AuthContext = createContext<AuthProps>({});

export const useAuth = () => {
  return useContext(AuthContext);
};

export const AuthProvider = ({ children }: any) => {
  const [authState, setAuthState] = useState<{
    token: string | null;
    authenticated: boolean | null;
    user: any;
    isTrade: boolean;
    didInitialFetch: boolean;
  }>({
    token: null,
    authenticated: null,
    user: null,
    isTrade: undefined,
    didInitialFetch: false,
  });

  const [, setButtonNumbers] = useAtom(buttonNumbersAtom);
  useEffect(() => {
    setupAxiosInterceptors();
    const loadToken = async () => {
      const token = await AsyncStorage.getItem(TOKEN_KEY);
      const handleNotAuthenticated = () => {
        setAuthState({
          token: '',
          authenticated: false,
          user: null,
          isTrade: false,
          didInitialFetch: true,
        });
      };

      if (token) {
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        try {
          const res = await axios.request({
            url: `${API_URL}/api/users/me`,
            method: 'get',
          });
          console.log('API /api/users/me', res.data);

          if (res.data.success && res.data.data) {
            setAuthState({
              token: token,
              authenticated: true,
              user: res.data.data,
              isTrade: (res.data.data.user_type || []).includes('Tradesperson'),
              didInitialFetch: true,
            });
          } else {
            handleNotAuthenticated();
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          trySilentLogin();
        }
      } else {
        trySilentLogin();
      }
    };

    const trySilentLogin = async () => {
      try {
        const credentials = await KeychainService.getCredentials();
        if (credentials) {
          const result = await login(credentials.username, credentials.password);
          if (!result?.data?.success) {
            setAuthState({
              token: '',
              authenticated: false,
              user: null,
              isTrade: false,
              didInitialFetch: true,
            });
          }
        } else {
          setAuthState({
            token: '',
            authenticated: false,
            user: null,
            isTrade: false,
            didInitialFetch: true,
          });
        }
      } catch (error) {
        console.error('Silent login error:', error);
        setAuthState({
          token: '',
          authenticated: false,
          user: null,
          isTrade: false,
          didInitialFetch: true,
        });
      }
    };

    loadToken();
  }, []);

  const setupAxiosInterceptors = () => {
    axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const credentials = await KeychainService.getCredentials();
            if (credentials) {
              const loginResult = await login(credentials.username, credentials.password);
              if (loginResult?.data?.success) {
                return axios(originalRequest);
              }
            }

            return Promise.reject(error);
          } catch (refreshError) {
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  };

  const loadUser = async () => {
    const res = await axios.request({
      url: `${API_URL}/api/users/me`,
      method: 'get',
    });
    if (res.data.success && res.data.data) {
      setAuthState({
        token: authState.token,
        authenticated: true,
        user: res.data.data,
        isTrade: (res.data.data.user_type || []).includes('Tradesperson'),
        didInitialFetch: true,
      });
    }
  };

  const updateUser = async (body) => {
    try {
      console.log('url', `${API_URL}/api/users/${authState.user.id}`);
      const res = await axios.request({
        url: `${API_URL}/api/users/${authState.user.id}`,
        data: body,
        method: 'put',
      });
      if (res.data.success && res.data.data) {
        setAuthState({
          token: authState.token,
          authenticated: true,
          user: res.data.data,
          isTrade: (res.data.data.user_type || []).includes('Tradesperson'),
          didInitialFetch: true,
        });
      }
      return { success: true, msg: 'Profile updated', data: res.data.data };
    } catch (error) {
      return { success: false, msg: error.message || error.response.data.msg };
    }
  };

  const registerWP = async (params) => {
    const {
      email,
      name,
      type,
      trade,
      qualifications,
      documents,
      companyAddress1,
      companyContact,
      postcode,
      orderNumber,
      orderContactName,
      orderContactEmail,
      orderDocuments,
    } = params;
    const data = {
      email,
      user_name: name,
      ...(type === 'Tradesperson'
        ? { trade, qualifications, documents }
        : {
            company_address_1: companyAddress1,
            company_contact: companyContact,
            order_number: orderNumber,
            order_contact_name: orderContactName,
            invoicing_contact_email: orderContactEmail,
            order_documents: orderDocuments,
          }),
      postcode,
      user_type: type,
    };
    try {
      const res = await axios.post(`${API_URL}/api/register`, JSON.stringify(data), {
        headers: {
          'Content-Type': 'application/json',
        },
      });
      return res.data;
    } catch (error) {
      return { error: true, msg: error.message || error.response.data.msg };
    }
  };

  const handleSuccess = async (idToken, res) => {
    setAuthState({
      token: idToken,
      authenticated: true,
      user: res.data,
      isTrade: (res.data.user_type || []).includes('Tradesperson'),
      didInitialFetch: true,
    });
    global.token = idToken;
    await AsyncStorage.setItem(TOKEN_KEY, idToken);
    return res;
  };

  const register = async (params: registerProps) => {
    const {
      email,
      password,
      name,
      trade,
      postcode,
      qualifications,
      type,
      documents,
      companyAddress1,
      companyContact,
      orderNumber,
      orderContactName,
      orderContactEmail,
      orderDocuments,
    } = params;

    try {
      const result = await auth().createUserWithEmailAndPassword(email, password);
      const idToken = await result.user?.getIdToken();
      if (idToken) {
        axios.defaults.headers.common['Authorization'] = `Bearer ${idToken}`;
        const res = await registerWP({
          email,
          name,
          type,
          trade,
          qualifications,
          documents,
          companyAddress1,
          companyContact,
          postcode,
          orderNumber,
          orderContactName,
          orderContactEmail,
          orderDocuments,
        });
        if (res.success) {
          return handleSuccess(idToken, res);
        } else if (res.success === false && res.data) {
          return { error: true, msg: res.message };
        }
      } else {
        return { error: true, msg: 'Could not get token' };
      }
    } catch (error1) {
      if (error1.code === 'auth/email-already-in-use') {
        return handleEmailAlreadyInUse(params);
      }

      return {
        error: true,
        msg: error1?.message,
      };
    }
  };

  const handleEmailAlreadyInUse = async (params) => {
    const { email, password } = params;

    try {
      const firebaseLogin = await auth().signInWithEmailAndPassword(email, password);
      const idToken = await firebaseLogin.user?.getIdToken();

      if (idToken) {
        axios.defaults.headers.common['Authorization'] = `Bearer ${idToken}`;

        try {
          const user = await axios.post(`${API_URL}/api/login`, null, {
            headers: {
              'Content-Type': 'application/json',
            },
          });
          if ((user.data.success && user.data.data) || (user.data.success === false && user.data.data)) {
            return { error: true, msg: 'Email already in use' };
          } else {
            const res = await registerWP(params);
            if (res.success) {
              return handleSuccess(idToken, res);
            } else if (res.success === false && res.data) {
              return { error: true, msg: res.message };
            }
          }
        } catch (e) {
          return {
            error: true,
            msg: e?.message || e?.response?.data?.msg || 'Error',
          };
        }
      }
    } catch (error) {
      return {
        error: true,
        msg: 'Email already in use',
      };
    }
  };

  const login = async (email: string, password: string, token?: string) => {
    try {
      if (token && email === '' && password === '') {
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        const res = await axios.request({
          url: `${API_URL}/api/login`,
          method: 'post',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        if (res.data.success === false) {
          AsyncStorage.removeItem(TOKEN_KEY);
          return { error: true, msg: res.data.message };
        } else if (res.data.success) {
          setAuthState({
            token: token,
            authenticated: true,
            user: res.data.data,
            isTrade: (res.data.data.user_type || []).includes('Tradesperson'),
            didInitialFetch: true,
          });
          await AsyncStorage.setItem(TOKEN_KEY, token);
          global.token = token;

          OneSignal.login(email);
          OneSignal.User.addEmail(email);
          return res;
        }
      } else {
        const data = await auth().signInWithEmailAndPassword(email, password);
        const idToken = await data.user?.getIdToken();
        console.log('idToken', idToken);
        if (idToken) {
          axios.defaults.headers.common['Authorization'] = `Bearer ${idToken}`;
          const res = await axios.request({
            url: `${API_URL}/api/login`,
            method: 'post',
            headers: {
              'Content-Type': 'application/json',
            },
          });
          if (res.data.success === false) {
            return { error: true, msg: res.data.message };
          } else if (res.data.success) {
            setAuthState({
              token: idToken,
              authenticated: true,
              user: res.data.data,
              isTrade: (res.data.data.user_type || []).includes('Tradesperson'),
              didInitialFetch: true,
            });
            await AsyncStorage.setItem(TOKEN_KEY, idToken);
            global.token = idToken;

            OneSignal.login(email);
            OneSignal.User.addEmail(email);

            return res;
          }
        } else {
          return { error: true, msg: 'Could not get token' };
        }
      }
    } catch (e) {
      return { error: true, msg: e.message || (e as any).response.data.msg };
    }
  };

  const forgotPassword = async (email: string) => {
    try {
      await auth().sendPasswordResetEmail(email);
      return { success: true, msg: 'Password reset email sent. Please check your inbox.' };
    } catch (error: any) {
      console.error('Forgot password error:', error);
      let errorMessage = 'Failed to send password reset email';
      if (error.code === 'auth/user-not-found') {
        errorMessage = 'No account found with this email address';
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = 'Please enter a valid email address';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many requests. Please try again later';
      }
      return { success: false, msg: errorMessage };
    }
  };

  const logout = async () => {
    await AsyncStorage.removeItem(TOKEN_KEY);

    setButtonNumbers({
      applications: 0,
      currentJobs: 0,
      profile: 0,
      messages: 0,
      news: 0,
    });

    OneSignal.logout();

    axios.defaults.headers.common['Authorization'] = '';
    setAuthState({
      token: null,
      authenticated: false,
      user: null,
      isTrade: undefined,
      didInitialFetch: true,
    });

    console.log('Logout completed, navigating to: Login');
  };

  const value = {
    onRegister: register,
    onLogin: login,
    onLogout: logout,
    onForgotPassword: forgotPassword,
    authState,
    isTrade: authState.isTrade,
    loadUser,
    updateUser,
  };
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
