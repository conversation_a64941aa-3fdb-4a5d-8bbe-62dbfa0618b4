import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, getListJobsQueryWP, getWPFilterORM, putWP } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { sendNoti } from "@/@common-utils/sendNoti";
import { syncJobFavorite } from "@/@common-utils/syncJobPreferences";

const setFavoriteJobsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "POST") {
      const { jobId, jobData } = req.body;
      // Helper function to send favorite notification
      const sendFavoriteNotification = async (jobId, tradeUserName, jobData = null) => {
        try {
          let businessOwnerEmail, businessOwnerName, jobDescription;

          // Use provided jobData if available to avoid queries
          if (jobData && jobData.businessOwnerEmail) {
            businessOwnerEmail = jobData.businessOwnerEmail;
            businessOwnerName = jobData.businessOwnerName;
            jobDescription = jobData.jobDescription;
            
            // Don't send notification if tradesperson favorites their own job
            if (businessOwnerEmail === user.email) {
              console.log('User favorited their own job, skipping notification');
              return;
            }
          } else {
            // Fallback to queries if jobData not provided (backward compatibility)
            const jobDetails = await getWP(`/jobs/${jobId}`);
            if (!jobDetails || !jobDetails.user_post || !jobDetails.user_post[0]) {
              console.log('Job details or business owner not found for notification');
              return;
            }

            const businessOwnerId = jobDetails.user_post[0].id || jobDetails.user_post[0].ID;
            const businessOwner = await getWP(`/app_user/${businessOwnerId}?_fields=email,user_name`);
            
            if (!businessOwner || !businessOwner.email) {
              console.log('Business owner email not found for notification');
              return;
            }

            businessOwnerEmail = businessOwner.email;
            businessOwnerName = businessOwner.user_name;
            jobDescription = jobDetails.task || jobDetails.title || 'Unknown job';

            // Don't send notification if tradesperson favorites their own job
            if (businessOwnerEmail === user.email) {
              console.log('User favorited their own job, skipping notification');
              return;
            }
          }

          const notificationMessage = `"${tradeUserName}" has favourited your job "${jobDescription}"`;

          await sendNoti(
            businessOwnerEmail,
            {
              title: "Job Favourited",
              text: notificationMessage,
              html: `<p>${notificationMessage}</p>`,
            },
            {
              mobile: true,
              mail: false,
            },
            "ApplyJobDetail",
            {
              activeJob: {
                id: jobId,
                task: jobDescription,
                user_post: [{
                  email: businessOwnerEmail,
                  user_name: businessOwnerName
                }]
              },
              isNoti: true,
            }
          );

          console.log(`Favorite notification sent to ${businessOwnerEmail}`);
        } catch (error) {
          console.error('Error sending favorite notification:', error);
        }
      };

      const currentUser = await getWPFilterORM(`/app_user?_fields=id,email,user_name`, {
        email: user.email
      })

      if (!currentUser || !currentUser[0]) {
        return res.json({ success: false, code: 403, message: "Cannot find user on database" });
      }

      const userId = currentUser[0].id;

      // Get current job to check if user already favorited it
      const job = await getWP(`/jobs/${jobId}`);
      if (!job || job.code) {
        return res.json({ success: false, code: 404, message: "Job not found" });
      }

      // Check current favorite status from denormalized field
      const favoritedByUsers = job.favorited_by_users || [];
      const currentlyFavorited = Array.isArray(favoritedByUsers)
        ? favoritedByUsers.includes(String(userId))
        : false;

      // Toggle favorite status
      const action = currentlyFavorited ? 'remove' : 'add';
      const success = await syncJobFavorite(jobId, userId, action);

      if (!success) {
        return res.json({ success: false, code: 500, message: "Failed to update favorite status" });
      }

      // Send notification when job is added to favorites (not removed)
      if (action === 'add' && !jobData?.isFavorite) {
        await sendFavoriteNotification(jobId, currentUser[0].user_name || 'Someone', jobData);
      }


      res.json({
        success: true,
        message: `Job ${action === 'add' ? 'added to' : 'removed from'} favorites`,
        data: {
          jobId,
          userId,
          action,
          isFavorite: action === 'add'
        }
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default setFavoriteJobsAPI;
