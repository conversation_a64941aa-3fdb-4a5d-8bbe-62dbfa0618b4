import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, getListJobsQueryWP, getWPFilterORM, putWP } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { sendNoti } from "@/@common-utils/sendNoti";
import { title } from "process";

const setFavoriteJobsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "POST") {
      const { jobId, jobData } = req.body;
      // Helper function to send favorite notification
      const sendFavoriteNotification = async (jobId, tradeUserName, jobData = null) => {
        try {
          let businessOwnerEmail, businessOwnerName, jobDescription;

          // Use provided jobData if available to avoid queries
          if (jobData && jobData.businessOwnerEmail) {
            businessOwnerEmail = jobData.businessOwnerEmail;
            businessOwnerName = jobData.businessOwnerName;
            jobDescription = jobData.jobDescription;
            
            // Don't send notification if tradesperson favorites their own job
            if (businessOwnerEmail === user.email) {
              console.log('User favorited their own job, skipping notification');
              return;
            }
          } else {
            // Fallback to queries if jobData not provided (backward compatibility)
            const jobDetails = await getWP(`/jobs/${jobId}`);
            if (!jobDetails || !jobDetails.user_post || !jobDetails.user_post[0]) {
              console.log('Job details or business owner not found for notification');
              return;
            }

            const businessOwnerId = jobDetails.user_post[0].id || jobDetails.user_post[0].ID;
            const businessOwner = await getWP(`/app_user/${businessOwnerId}?_fields=email,user_name`);
            
            if (!businessOwner || !businessOwner.email) {
              console.log('Business owner email not found for notification');
              return;
            }

            businessOwnerEmail = businessOwner.email;
            businessOwnerName = businessOwner.user_name;
            jobDescription = jobDetails.task || jobDetails.title || 'Unknown job';

            // Don't send notification if tradesperson favorites their own job
            if (businessOwnerEmail === user.email) {
              console.log('User favorited their own job, skipping notification');
              return;
            }
          }

          const notificationMessage = `"${tradeUserName}" has favourited your job "${jobDescription}"`;

          await sendNoti(
            businessOwnerEmail,
            {
              title: "Job Favourited",
              text: notificationMessage,
              html: `<p>${notificationMessage}</p>`,
            },
            {
              mobile: true,
              mail: false,
            },
            "ApplyJobDetail",
            {
              activeJob: {
                id: jobId,
                task: jobDescription,
                user_post: [{
                  email: businessOwnerEmail,
                  user_name: businessOwnerName
                }]
              },
              isNoti: true,
            }
          );

          console.log(`Favorite notification sent to ${businessOwnerEmail}`);
        } catch (error) {
          console.error('Error sending favorite notification:', error);
        }
      };

      const currentUser = await getWPFilterORM(`/app_user?_fields=id,email,user_name`, {
        email: user.email
      })

      if (!currentUser) {
        return res.json({ success: false, code: 403, message: "Cannot find user on database" });
      }

      const userId = currentUser[0].id

      const currentRecord = await getWPFilterORM(`/user_favorited_job`, {
        user_id: userId,
      })


      if('data' in currentRecord && 'status' in currentRecord.data && currentRecord.data.status === 404) {
        return res.json({
          success: false,
        });
      }

      const queryURL = `/user_favorited_job`
      let queryResult :any;

      // If the record does not exist, create a new one
      if(currentRecord.length === 0) {
        queryResult = await postWP(queryURL, {
          user_id: userId,
          job_id: [jobId],
          title:`Favourite job for ${userId}`,
          status:'publish',
        })
        
        // Send notification for first favorite
        if (queryResult && !jobData?.isFavorite) {
          await sendFavoriteNotification(jobId, currentUser[0].user_name || 'Someone', jobData);
        }
      }else{
        const db_jobIds = currentRecord[0].job_id
        
        // If this jobID is already in the list, remove it
        if (db_jobIds.includes(String(jobId))) {
          console.log("remove job id", jobId);
          let newJobIds = db_jobIds.filter((id) => id !== String(jobId));
          queryResult = await putWP(`${queryURL}/${currentRecord[0].id}`, {
            job_id: newJobIds,
          })
        }else{
          // If this jobID is not in the list, add it
          console.log("add job id", jobId);
          let newJobIds = db_jobIds.filter((id) => id);
          queryResult =await putWP(`${queryURL}/${currentRecord[0].id}`, {
            job_id: [...newJobIds,jobId],
          })
          
          // Send notification when job is added to favorites
          if (queryResult && !jobData?.isFavorite) {
            await sendFavoriteNotification(jobId, currentUser[0].user_name || 'Someone', jobData);
          }
        }
      }


      res.json({
        user,
        currentUser,
        success: true,
        currentRecord,
        queryResult
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default setFavoriteJobsAPI;
