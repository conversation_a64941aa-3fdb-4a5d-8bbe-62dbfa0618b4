import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, getListJobsQueryWP, getWPFilterORM, putWP } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { title } from "process";

const hideJobsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "POST") {
      const { jobIds } = req.body;
      const currentUser = await getWPFilterORM(`/app_user?_fields=id,email`, {
        email: user.email
      })

      if (!currentUser) {
        return res.json({ success: false, code: 403, message: "Cannot find user on database" });
      }

      const userId = currentUser[0].id

      const currentRecord = await getWPFilterORM(`/user_hided_job`, {
        user_id: userId,
      })

      console.log({jobIds})

      if(currentRecord.length === 0) {
        await postWP(`/user_hided_job`, {
          user_id: userId,
          job_id: jobIds,
          title:`Hide job ${jobIds.join(", ")}`,
          status:'publish',
        })
      }else{

        const allJobId = [...Array.from(new Set([...currentRecord[0].job_id,...jobIds.map(item=>item.toString())]))]
        console.log({allJobId})
        await putWP(`/user_hided_job/${currentRecord[0].id}`, {
          job_id: allJobId,
          title:`Hide job ${jobIds.join(", ")}`,
          status:'publish',

        })
      }

      // const jobs = await getWPFilterORM(`/hided_jobs`, {
      //   job_id: req.body.jobId,
      // });
      // if (!jobs) {
      //   res.json({
      //     success: false,
      //     message: "Failed to get job.",
      //   });
      //   return;
      // }

      res.json({
        user,
        currentUser,
        success: true,
        currentRecord,
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default hideJobsAPI;
