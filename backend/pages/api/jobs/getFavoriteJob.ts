import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const getFavoriteJobsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "GET") {
      const currentUser = await getWPFilterORM(`/app_user?_fields=id,email`, {
        email: user.email
      })

      if (!currentUser || !currentUser[0]) {
        return res.json({ success: false, code: 403, message: "Cannot find user on database" });
      }

      const userId = currentUser[0].id;

      // OPTIMIZED: Get favorite jobs from denormalized fields
      const allJobs = await getWPFilterORM('/jobs', {}, 'id,favorited_by_users');

      const favoriteJobs = allJobs.filter(job => {
        const favoritedByUsers = job.favorited_by_users || [];
        return Array.isArray(favoritedByUsers) && favoritedByUsers.includes(String(userId));
      });

      res.json({
        success: true,
        data: favoriteJobs.map(job => job.id),
        total: favoriteJobs.length
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default getFavoriteJobsAPI;
