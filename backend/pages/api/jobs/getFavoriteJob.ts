import { allowCORS } from "@/@common-utils/allowCORS";
import { getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const getFavoriteJobsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "GET") {
      const currentUser = await getWPFilterORM(`/app_user?_fields=id,email`, {
        email: user.email
      })

      if (!currentUser) {
        return res.json({ success: false, code: 403, message: "Cannot find user on database" });
      }

      const userId = currentUser[0].id

      const currentRecord = await getWPFilterORM(`/user_favorited_job`, {
        user_id: userId,
      })

      res.json({
        success: true,
        data:currentRecord[0]?.job_id || []
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default getFavoriteJobsAPI;
