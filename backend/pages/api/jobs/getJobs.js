import { allowCORS } from "@/@common-utils/allowCORS";
import { postWP, getWP, getListJobsQueryWP, getWPFilterORM } from "@/@common-utils/callWP";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";

const jobsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    if (!user) return res.json({ success: false, code: 403, message: "You need to login first." });

    if (req.method === "GET") {
      const [currentUser, jobs] = await Promise.all([
        getWPFilterORM(`/app_user?_fields=id,email`, { email: user.email }),
        getListJobsQueryWP(`/jobs`, req.query.trade === "All" ? {...req.query, trade: undefined} : req.query)
      ]);

      if (!jobs) {
        return res.json({
          success: false,
          message: "Failed to get job.",
        });
      }

      const [hidedJob, existingReview] = await Promise.all([
        getWPFilterORM(`/user_hided_job`, { user_id: currentUser[0].id }),
        (async () => {
          const distinctUserId = [...new Set(jobs.map(item => item?.user_post[0]?.id))];
          const reviews = {};
          await Promise.all(
            distinctUserId.map(async id => {
              reviews[id] = await getWPFilterORM(`/reviews`, { review_recipient: id }, "rate_number");
            })
          );
          return reviews;
        })()
      ]);
      const hiddenJobId = hidedJob[0]?.job_id;
      const filteredJobs = hiddenJobId 
        ? jobs.filter(item => !hiddenJobId.includes(String(item?.id)))
        : jobs;
      const jobWithReview = filteredJobs.map(item => ({
        ...item,
        reviews: existingReview[item?.user_post[0]?.id] || null
      }));
      return res.json({
        success: true,
        data: jobWithReview,
        jobs,
        hidedJob
      });
    }
  } catch (error) {
    console.log('error', error);
  }
};

export default jobsAPI;
