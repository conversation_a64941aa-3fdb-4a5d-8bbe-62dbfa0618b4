import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { migrateJobPreferences, syncJobFavorite, syncJobHidden } from "@/@common-utils/syncJobPreferences";

/**
 * Admin endpoint to migrate job preferences to denormalized fields
 * This should be run once to populate favorited_by_users and hidden_by_users for all jobs
 */
const migrateJobPreferencesAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    
    if (!user) {
      return res.json({ 
        success: false, 
        code: 403, 
        message: "You need to login first." 
      });
    }

    if (req.method === "POST") {
      const { action, jobId, userId, type } = req.body;

      if (action === "migrate_all") {
        console.log("🚀 Starting migration of all job preferences...");
        
        const results = await migrateJobPreferences();
        
        return res.json({
          success: true,
          message: `Migration completed: ${results.success} jobs updated, ${results.failed} failed`,
          data: results
        });
        
      } else if (action === "sync_single" && jobId && userId && type) {
        console.log(`🔄 Syncing ${type} for job ${jobId}, user ${userId}...`);
        
        const syncFunction = type === 'favorite' ? syncJobFavorite : syncJobHidden;
        const success = await syncFunction(jobId, userId, 'add');
        
        return res.json({
          success: success,
          message: success 
            ? `Successfully synced ${type} for job ${jobId}`
            : `Failed to sync ${type} for job ${jobId}`
        });
        
      } else {
        return res.json({
          success: false,
          code: 400,
          message: "Invalid action. Use 'migrate_all' or 'sync_single' with jobId, userId, and type"
        });
      }
    }

    if (req.method === "GET") {
      return res.json({
        success: true,
        message: "Job preferences migration endpoint ready",
        usage: {
          migrate_all: "POST with { action: 'migrate_all' }",
          sync_single: "POST with { action: 'sync_single', jobId: 'JOB_ID', userId: 'USER_ID', type: 'favorite|hidden' }"
        }
      });
    }

  } catch (error) {
    console.error('Job preferences migration API error:', error);
    return res.json({
      success: false,
      code: 500,
      message: "Migration failed",
      error: error.message
    });
  }
};

export default migrateJobPreferencesAPI;
