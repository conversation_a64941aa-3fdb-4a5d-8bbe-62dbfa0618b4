import { allowCORS } from "@/@common-utils/allowCORS";
import { checkFirebaseAuth } from "@/@common-utils/checkFirebaseAuth";
import { migrateAllUserReviewSummaries, updateUserReviewSummary } from "@/@common-utils/syncReviews";

/**
 * Admin endpoint to migrate review summaries
 * This should be run once to populate review_summary for all existing users
 */
const migrateReviewsAPI = async (req, res) => {
  try {
    await allowCORS(req, res);
    const user = await checkFirebaseAuth(req);
    
    if (!user) {
      return res.json({ 
        success: false, 
        code: 403, 
        message: "You need to login first." 
      });
    }

    if (req.method === "POST") {
      const { action, userId } = req.body;

      if (action === "migrate_all") {
        console.log("🚀 Starting migration of all user review summaries...");
        
        const results = await migrateAllUserReviewSummaries();
        
        return res.json({
          success: true,
          message: `Migration completed: ${results.success} users updated, ${results.failed} failed`,
          data: results
        });
        
      } else if (action === "migrate_single" && userId) {
        console.log(`🔄 Updating review summary for user ${userId}...`);
        
        const success = await updateUserReviewSummary(userId);
        
        return res.json({
          success: success,
          message: success 
            ? `Successfully updated review summary for user ${userId}`
            : `Failed to update review summary for user ${userId}`
        });
        
      } else {
        return res.json({
          success: false,
          code: 400,
          message: "Invalid action. Use 'migrate_all' or 'migrate_single' with userId"
        });
      }
    }

    if (req.method === "GET") {
      return res.json({
        success: true,
        message: "Migration endpoint ready",
        usage: {
          migrate_all: "POST with { action: 'migrate_all' }",
          migrate_single: "POST with { action: 'migrate_single', userId: 'USER_ID' }"
        }
      });
    }

  } catch (error) {
    console.error('Migration API error:', error);
    return res.json({
      success: false,
      code: 500,
      message: "Migration failed",
      error: error.message
    });
  }
};

export default migrateReviewsAPI;
