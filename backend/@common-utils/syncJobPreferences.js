/**
 * Sync job user preferences (favorites and hidden) to denormalized fields
 * This eliminates the need for separate API calls to get user preferences
 */

import { getWP, putWP, getWPFilterORM } from "./callWP.js";

/**
 * Add or remove user from job's favorited_by_users array
 * @param {string|number} jobId - Job ID
 * @param {string|number} userId - User ID
 * @param {string} action - 'add' or 'remove'
 * @returns {Promise<boolean>} - Success status
 */
export const syncJobFavorite = async (jobId, userId, action) => {
  try {
    console.log(`Syncing favorite: ${action} user ${userId} for job ${jobId}`);
    
    // Get current job data
    const job = await getWP(`/jobs/${jobId}`);
    if (!job || job.code) {
      console.error(`Job ${jobId} not found`);
      return false;
    }

    // Get current favorited_by_users array from text content
    let favoritedByUsers = [];
    if (job.favorited_by_users) {
      if (typeof job.favorited_by_users === 'string') {
        // Parse comma-separated string: "123,456,789"
        favoritedByUsers = job.favorited_by_users
          .split(',')
          .map(id => id.trim())
          .filter(id => id.length > 0);
      } else if (Array.isArray(job.favorited_by_users)) {
        favoritedByUsers = job.favorited_by_users;
      }
    }

    // Ensure all values are strings for consistent comparison
    favoritedByUsers = favoritedByUsers.map(id => String(id));
    const userIdStr = String(userId);

    // Update array based on action
    if (action === 'add') {
      if (!favoritedByUsers.includes(userIdStr)) {
        favoritedByUsers.push(userIdStr);
      }
    } else if (action === 'remove') {
      favoritedByUsers = favoritedByUsers.filter(id => id !== userIdStr);
    }

    // Update job with comma-separated string
    const updateResult = await putWP(`/jobs/${jobId}`, {
      favorited_by_users: favoritedByUsers.join(',')
    });

    if (updateResult && !updateResult.code) {
      console.log(`✅ Updated job ${jobId} favorites: ${favoritedByUsers.length} users`);
      return true;
    } else {
      console.error(`❌ Failed to update job ${jobId}:`, updateResult);
      return false;
    }

  } catch (error) {
    console.error(`❌ Error syncing favorite for job ${jobId}:`, error);
    return false;
  }
};

/**
 * Add or remove user from job's hidden_by_users array
 * @param {string|number} jobId - Job ID
 * @param {string|number} userId - User ID
 * @param {string} action - 'add' or 'remove'
 * @returns {Promise<boolean>} - Success status
 */
export const syncJobHidden = async (jobId, userId, action) => {
  try {
    console.log(`Syncing hidden: ${action} user ${userId} for job ${jobId}`);
    
    // Get current job data
    const job = await getWP(`/jobs/${jobId}`);
    if (!job || job.code) {
      console.error(`Job ${jobId} not found`);
      return false;
    }

    // Get current hidden_by_users array from text content
    let hiddenByUsers = [];
    if (job.hidden_by_users) {
      if (typeof job.hidden_by_users === 'string') {
        // Parse comma-separated string: "123,456,789"
        hiddenByUsers = job.hidden_by_users
          .split(',')
          .map(id => id.trim())
          .filter(id => id.length > 0);
      } else if (Array.isArray(job.hidden_by_users)) {
        hiddenByUsers = job.hidden_by_users;
      }
    }

    // Ensure all values are strings for consistent comparison
    hiddenByUsers = hiddenByUsers.map(id => String(id));
    const userIdStr = String(userId);

    // Update array based on action
    if (action === 'add') {
      if (!hiddenByUsers.includes(userIdStr)) {
        hiddenByUsers.push(userIdStr);
      }
    } else if (action === 'remove') {
      hiddenByUsers = hiddenByUsers.filter(id => id !== userIdStr);
    }

    // Update job with comma-separated string
    const updateResult = await putWP(`/jobs/${jobId}`, {
      hidden_by_users: hiddenByUsers.join(',')
    });

    if (updateResult && !updateResult.code) {
      console.log(`✅ Updated job ${jobId} hidden: ${hiddenByUsers.length} users`);
      return true;
    } else {
      console.error(`❌ Failed to update job ${jobId}:`, updateResult);
      return false;
    }

  } catch (error) {
    console.error(`❌ Error syncing hidden for job ${jobId}:`, error);
    return false;
  }
};

/**
 * Migrate existing user preferences to denormalized job fields
 * @returns {Promise<{success: number, failed: number}>} - Migration results
 */
export const migrateJobPreferences = async () => {
  try {
    console.log('🚀 Starting migration: Moving user preferences to job denormalized fields...');
    
    // Get all jobs
    const allJobs = await getWPFilterORM('/jobs', {}, 'id');
    if (!allJobs || !Array.isArray(allJobs)) {
      console.error('❌ Failed to get jobs for migration');
      return { success: 0, failed: 0 };
    }

    // Get all user preferences
    const [allFavorites, allHidden] = await Promise.all([
      getWPFilterORM('/user_favorited_job', {}),
      getWPFilterORM('/user_hided_job', {})
    ]);

    // Create lookup maps: jobId -> [userIds]
    const favoritesByJob = {};
    const hiddenByJob = {};

    // Process favorites
    if (allFavorites && Array.isArray(allFavorites)) {
      allFavorites.forEach(fav => {
        const jobIds = fav.job_id || [];
        const userId = String(fav.user_id);
        
        jobIds.forEach(jobId => {
          if (!favoritesByJob[jobId]) favoritesByJob[jobId] = [];
          favoritesByJob[jobId].push(userId);
        });
      });
    }

    // Process hidden jobs
    if (allHidden && Array.isArray(allHidden)) {
      allHidden.forEach(hidden => {
        const jobIds = hidden.job_id || [];
        const userId = String(hidden.user_id);
        
        jobIds.forEach(jobId => {
          if (!hiddenByJob[jobId]) hiddenByJob[jobId] = [];
          hiddenByJob[jobId].push(userId);
        });
      });
    }

    // Update each job with denormalized data
    let successCount = 0;
    let failedCount = 0;

    for (const job of allJobs) {
      const jobId = job.id;
      const favoritedBy = favoritesByJob[jobId] || [];
      const hiddenBy = hiddenByJob[jobId] || [];

      try {
        await putWP(`/jobs/${jobId}`, {
          favorited_by_users: favoritedBy.join(','),
          hidden_by_users: hiddenBy.join(',')
        });
        
        successCount++;
        console.log(`Updated job ${jobId}: ${favoritedBy.length} favorites, ${hiddenBy.length} hidden`);
      } catch (error) {
        failedCount++;
        console.error(`Failed to update job ${jobId}:`, error);
      }
    }

    console.log(`🎉 Migration completed: ${successCount} success, ${failedCount} failed`);
    return { success: successCount, failed: failedCount };

  } catch (error) {
    console.error('❌ Migration failed:', error);
    return { success: 0, failed: 0 };
  }
};

/**
 * Batch sync multiple jobs for a user action
 * @param {Array<string|number>} jobIds - Array of job IDs
 * @param {string|number} userId - User ID
 * @param {string} action - 'add' or 'remove'
 * @param {string} type - 'favorite' or 'hidden'
 * @returns {Promise<{success: number, failed: number}>} - Sync results
 */
export const batchSyncJobPreferences = async (jobIds, userId, action, type) => {
  console.log(`Batch syncing ${type}: ${action} user ${userId} for ${jobIds.length} jobs`);
  
  const syncFunction = type === 'favorite' ? syncJobFavorite : syncJobHidden;
  let successCount = 0;
  let failedCount = 0;

  // Process in small batches to avoid overwhelming the server
  const batchSize = 3;
  for (let i = 0; i < jobIds.length; i += batchSize) {
    const batch = jobIds.slice(i, i + batchSize);
    
    const results = await Promise.allSettled(
      batch.map(jobId => syncFunction(jobId, userId, action))
    );
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value === true) {
        successCount++;
      } else {
        failedCount++;
        console.error(`Failed to sync ${type} for job ${batch[index]}:`, result.reason);
      }
    });
    
    // Small delay between batches
    if (i + batchSize < jobIds.length) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }

  console.log(`✅ Batch sync completed: ${successCount} success, ${failedCount} failed`);
  return { success: successCount, failed: failedCount };
};
